2025/07/26-18:55:51.051 5350 Reusing MANIFEST C:\Users\<USER>\AppData\Local\Temp\flutter_tools.4198d0d\flutter_tools_chrome_device.adf7cfb0\Default\IndexedDB\http_localhost_27247.indexeddb.leveldb/MANIFEST-000001
2025/07/26-18:55:51.053 5350 Recovering log #7
2025/07/26-18:55:51.053 5350 Reusing old log C:\Users\<USER>\AppData\Local\Temp\flutter_tools.4198d0d\flutter_tools_chrome_device.adf7cfb0\Default\IndexedDB\http_localhost_27247.indexeddb.leveldb/000007.log 
2025/07/26-18:55:51.076 496c Level-0 table #11: started
2025/07/26-18:55:51.080 496c Level-0 table #11: 3335 bytes OK
2025/07/26-18:55:51.084 496c Delete type=0 #7
2025/07/26-18:55:51.085 6cd0 Manual compaction at level-0 from '\x00\x05\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x06\x00\x00\x00' @ 0 : 0; will stop at '\x00\x05\x00\x00\x05' @ 201 : 1
2025/07/26-18:55:51.085 6cd0 Compacting 1@0 + 1@1 files
2025/07/26-18:55:51.089 6cd0 Generated table #12@0: 62 keys, 3231 bytes
2025/07/26-18:55:51.089 6cd0 Compacted 1@0 + 1@1 files => 3231 bytes
2025/07/26-18:55:51.092 6cd0 compacted to: files[ 0 1 1 0 0 0 0 ]
2025/07/26-18:55:51.092 6cd0 Delete type=2 #11
2025/07/26-18:55:51.092 6cd0 Manual compaction at level-0 from '\x00\x05\x00\x00\x05' @ 201 : 1 .. '\x00\x06\x00\x00\x00' @ 0 : 0; will stop at (end)
